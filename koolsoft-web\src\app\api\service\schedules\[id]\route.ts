import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * DELETE /api/service/schedules/[id]
 * Delete a specific service schedule
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      // For now, return success response
      // In a real implementation, this would delete the record from the database
      console.log(`Deleting service schedule: ${id}`);

      return NextResponse.json({
        message: 'Service schedule deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service schedule:', error);
      return NextResponse.json(
        { error: 'Failed to delete service schedule' },
        { status: 500 }
      );
    }
  }
);
