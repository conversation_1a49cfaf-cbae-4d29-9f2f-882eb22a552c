import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceScheduleRepository } from '@/lib/repositories';

/**
 * DELETE /api/service/schedules/[id]
 * Delete a specific service schedule
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const serviceScheduleRepository = getServiceScheduleRepository();

      // Check if schedule exists
      const existingSchedule = await serviceScheduleRepository.findById(id);
      if (!existingSchedule) {
        return NextResponse.json(
          { error: 'Service schedule not found' },
          { status: 404 }
        );
      }

      // Delete the schedule
      await serviceScheduleRepository.delete(id);

      return NextResponse.json({
        message: 'Service schedule deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service schedule:', error);
      return NextResponse.json(
        { error: 'Failed to delete service schedule' },
        { status: 500 }
      );
    }
  }
);
