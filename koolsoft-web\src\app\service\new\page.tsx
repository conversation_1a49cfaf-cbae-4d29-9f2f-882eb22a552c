'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ServiceReportForm } from '@/components/service/service-report-form';
import { toast } from 'sonner';
import { createServiceReportSchema } from '@/lib/validations/service.schema';
import { z } from 'zod';

type ServiceReportFormData = z.infer<typeof createServiceReportSchema>;

export default function NewServiceReportPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: ServiceReportFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/service', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Service report created successfully');
        router.push(`/service/${result.serviceReport.id}`);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create service report');
      }
    } catch (error) {
      console.error('Error creating service report:', error);
      toast.error('Failed to create service report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/service');
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Create New Service Report</h1>
        <p className="text-muted-foreground">
          Fill in the details below to create a new service report.
        </p>
      </div>

      <ServiceReportForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  );
}
