import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { serviceSchedulingSchema } from '@/lib/validations/service.schema';

/**
 * GET /api/service/schedules
 * Get service schedules with optional filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      // For now, return mock data since we don't have a schedules table
      // In a real implementation, this would query the database
      const mockSchedules = [
        {
          id: '1',
          serviceReportId: 'sr-1',
          scheduledDate: new Date().toISOString(),
          technicianId: 'tech-1',
          estimatedDuration: 120,
          priority: 'MEDIUM',
          notes: 'Regular maintenance check',
          status: 'SCHEDULED',
          serviceReport: {
            id: 'sr-1',
            natureOfService: 'Air Conditioner Maintenance',
            customer: {
              name: 'ABC Company',
              city: 'Mumbai',
            },
          },
          technician: {
            id: 'tech-1',
            name: '<PERSON>',
          },
        },
      ];

      return NextResponse.json({
        schedules: mockSchedules,
        pagination: {
          page: 1,
          limit: 10,
          total: mockSchedules.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    } catch (error) {
      console.error('Error fetching service schedules:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service schedules' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/service/schedules
 * Create a new service schedule
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = serviceSchedulingSchema.parse(body);

      // For now, return success response
      // In a real implementation, this would create a record in the database
      const newSchedule = {
        id: `schedule-${Date.now()}`,
        ...validatedData,
        status: 'SCHEDULED',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return NextResponse.json(
        { 
          message: 'Service scheduled successfully',
          schedule: newSchedule 
        },
        { status: 201 }
      );
    } catch (error) {
      console.error('Error creating service schedule:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to create service schedule' },
        { status: 500 }
      );
    }
  }
);
