'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { DataTable } from '@/components/ui/data-table';
import { toast } from 'sonner';
import { 
  Calendar as CalendarIcon,
  Clock,
  Plus,
  Search,
  Filter,
  User,
  AlertCircle,
  CheckCircle,
  XCircle,
  Edit,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, addDays, startOfWeek, endOfWeek } from 'date-fns';

interface ServiceSchedule {
  id: string;
  serviceReportId: string;
  scheduledDate: string;
  technicianId?: string;
  estimatedDuration?: number;
  priority: string;
  notes?: string;
  status: string;
  serviceReport: {
    id: string;
    natureOfService: string;
    customer: {
      name: string;
      city: string;
    };
  };
  technician?: {
    id: string;
    name: string;
  };
}

interface ServiceReport {
  id: string;
  natureOfService: string;
  status: string;
  customer: {
    name: string;
    city: string;
  };
}

export default function ServiceSchedulingPage() {
  const [schedules, setSchedules] = useState<ServiceSchedule[]>([]);
  const [serviceReports, setServiceReports] = useState<ServiceReport[]>([]);
  const [technicians, setTechnicians] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewScheduleForm, setShowNewScheduleForm] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Form state for new schedule
  const [newSchedule, setNewSchedule] = useState({
    serviceReportId: '',
    scheduledDate: new Date(),
    technicianId: '',
    estimatedDuration: 120, // 2 hours default
    priority: 'MEDIUM',
    notes: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [schedulesRes, reportsRes, techniciansRes] = await Promise.all([
        fetch('/api/service/schedules', { credentials: 'include' }),
        fetch('/api/service?status=OPEN&limit=100', { credentials: 'include' }),
        fetch('/api/users?role=EXECUTIVE&limit=100', { credentials: 'include' }),
      ]);

      if (schedulesRes.ok) {
        const schedulesData = await schedulesRes.json();
        setSchedules(schedulesData.schedules || []);
      }

      if (reportsRes.ok) {
        const reportsData = await reportsRes.json();
        setServiceReports(reportsData.serviceReports || []);
      }

      if (techniciansRes.ok) {
        const techniciansData = await techniciansRes.json();
        setTechnicians(techniciansData.users || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load scheduling data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSchedule = async () => {
    try {
      const response = await fetch('/api/service/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newSchedule),
      });

      if (response.ok) {
        toast.success('Service scheduled successfully');
        setShowNewScheduleForm(false);
        setNewSchedule({
          serviceReportId: '',
          scheduledDate: new Date(),
          technicianId: '',
          estimatedDuration: 120,
          priority: 'MEDIUM',
          notes: '',
        });
        loadData();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create schedule');
      }
    } catch (error) {
      console.error('Error creating schedule:', error);
      toast.error('Failed to create schedule');
    }
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    if (!confirm('Are you sure you want to delete this schedule?')) {
      return;
    }

    try {
      const response = await fetch(`/api/service/schedules/${scheduleId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        toast.success('Schedule deleted successfully');
        loadData();
      } else {
        toast.error('Failed to delete schedule');
      }
    } catch (error) {
      console.error('Error deleting schedule:', error);
      toast.error('Failed to delete schedule');
    }
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      LOW: { variant: 'secondary' as const, label: 'Low' },
      MEDIUM: { variant: 'default' as const, label: 'Medium' },
      HIGH: { variant: 'default' as const, label: 'High' },
      URGENT: { variant: 'destructive' as const, label: 'Urgent' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.MEDIUM;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SCHEDULED: { variant: 'secondary' as const, icon: Clock, label: 'Scheduled' },
      IN_PROGRESS: { variant: 'default' as const, icon: Clock, label: 'In Progress' },
      COMPLETED: { variant: 'default' as const, icon: CheckCircle, label: 'Completed' },
      CANCELLED: { variant: 'destructive' as const, icon: XCircle, label: 'Cancelled' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.SCHEDULED;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const filteredSchedules = schedules.filter(schedule => {
    const matchesSearch = searchTerm === '' || 
      schedule.serviceReport.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.serviceReport.natureOfService.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPriority = priorityFilter === 'all' || schedule.priority === priorityFilter;
    const matchesStatus = statusFilter === 'all' || schedule.status === statusFilter;

    return matchesSearch && matchesPriority && matchesStatus;
  });

  const columns = [
    {
      header: 'Scheduled Date',
      accessorKey: 'scheduledDate',
      cell: ({ row }: any) => format(new Date(row.original.scheduledDate), 'MMM dd, yyyy HH:mm'),
    },
    {
      header: 'Customer',
      accessorKey: 'serviceReport.customer.name',
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.serviceReport.customer.name}</div>
          <div className="text-sm text-muted-foreground">{row.original.serviceReport.customer.city}</div>
        </div>
      ),
    },
    {
      header: 'Service',
      accessorKey: 'serviceReport.natureOfService',
      cell: ({ row }: any) => (
        <div className="max-w-[200px] truncate" title={row.original.serviceReport.natureOfService}>
          {row.original.serviceReport.natureOfService}
        </div>
      ),
    },
    {
      header: 'Technician',
      accessorKey: 'technician.name',
      cell: ({ row }: any) => row.original.technician?.name || 'Unassigned',
    },
    {
      header: 'Duration',
      accessorKey: 'estimatedDuration',
      cell: ({ row }: any) => 
        row.original.estimatedDuration ? `${row.original.estimatedDuration} min` : '-',
    },
    {
      header: 'Priority',
      accessorKey: 'priority',
      cell: ({ row }: any) => getPriorityBadge(row.original.priority),
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }: any) => getStatusBadge(row.original.status),
    },
    {
      header: 'Actions',
      id: 'actions',
      cell: ({ row }: any) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {/* Edit functionality */}}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteSchedule(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Service Scheduling</h1>
          <p className="text-muted-foreground">
            Schedule and manage service appointments.
          </p>
        </div>
        <Button onClick={() => setShowNewScheduleForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Schedule Service
        </Button>
      </div>

      {/* New Schedule Form */}
      {showNewScheduleForm && (
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle>Schedule New Service</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="serviceReportId">Service Report *</Label>
                <Select
                  value={newSchedule.serviceReportId}
                  onValueChange={(value) => setNewSchedule(prev => ({ ...prev, serviceReportId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service report" />
                  </SelectTrigger>
                  <SelectContent>
                    {serviceReports.map((report) => (
                      <SelectItem key={report.id} value={report.id}>
                        {report.customer.name} - {report.natureOfService}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Scheduled Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !newSchedule.scheduledDate && 'text-muted-foreground'
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newSchedule.scheduledDate ? (
                        format(newSchedule.scheduledDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={newSchedule.scheduledDate}
                      onSelect={(date) => setNewSchedule(prev => ({ ...prev, scheduledDate: date || new Date() }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="technicianId">Technician</Label>
                <Select
                  value={newSchedule.technicianId}
                  onValueChange={(value) => setNewSchedule(prev => ({ ...prev, technicianId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select technician" />
                  </SelectTrigger>
                  <SelectContent>
                    {technicians.map((technician) => (
                      <SelectItem key={technician.id} value={technician.id}>
                        {technician.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedDuration">Duration (minutes)</Label>
                <Input
                  id="estimatedDuration"
                  type="number"
                  value={newSchedule.estimatedDuration}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, estimatedDuration: parseInt(e.target.value) }))}
                  placeholder="120"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={newSchedule.priority}
                  onValueChange={(value) => setNewSchedule(prev => ({ ...prev, priority: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="URGENT">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={newSchedule.notes}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes for the scheduled service"
                rows={3}
              />
            </div>

            <div className="flex items-center justify-end gap-4 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowNewScheduleForm(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateSchedule}>
                Schedule Service
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Schedules Table */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Service Schedules
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search schedules..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="URGENT">Urgent</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <DataTable
            columns={columns}
            data={filteredSchedules}
            loading={loading}
          />
        </CardContent>
      </Card>
    </div>
  );
}
