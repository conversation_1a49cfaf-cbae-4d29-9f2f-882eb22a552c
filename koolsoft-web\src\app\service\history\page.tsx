'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { toast } from 'sonner';
import {
  Search,
  Filter,
  Download,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Calendar,
  User,
  History
} from 'lucide-react';
import { format } from 'date-fns';

interface ServiceReport {
  id: string;
  reportDate: string;
  visitDate?: string;
  completionDate?: string;
  natureOfService: string;
  complaintType: string;
  status: string;
  customer: {
    id: string;
    name: string;
    city: string;
  };
  executive: {
    id: string;
    name: string;
  };
  details: any[];
}

export default function ServiceHistoryPage() {
  const [serviceReports, setServiceReports] = useState<ServiceReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [complaintTypeFilter, setComplaintTypeFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Load service reports
  useEffect(() => {
    loadServiceReports();
  }, [currentPage, statusFilter, complaintTypeFilter, searchTerm]);

  const loadServiceReports = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(complaintTypeFilter !== 'all' && { complaintType: complaintTypeFilter }),
        sortBy: 'reportDate',
        sortOrder: 'desc',
      });

      const response = await fetch(`/api/service?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setServiceReports(data.serviceReports || []);
        setTotalPages(data.pagination?.totalPages || 1);
      } else {
        toast.error('Failed to load service history');
      }
    } catch (error) {
      console.error('Error loading service history:', error);
      toast.error('Failed to load service history');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams({
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(complaintTypeFilter !== 'all' && { complaintType: complaintTypeFilter }),
      });

      const response = await fetch(`/api/service/export?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `service-history-${format(new Date(), 'yyyy-MM-dd')}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        toast.success('Service history exported successfully');
      } else {
        toast.error('Failed to export service history');
      }
    } catch (error) {
      console.error('Error exporting service history:', error);
      toast.error('Failed to export service history');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      OPEN: { variant: 'secondary' as const, icon: AlertCircle, label: 'Open' },
      IN_PROGRESS: { variant: 'default' as const, icon: Clock, label: 'In Progress' },
      COMPLETED: { variant: 'default' as const, icon: CheckCircle, label: 'Completed' },
      CANCELLED: { variant: 'destructive' as const, icon: XCircle, label: 'Cancelled' },
      PENDING: { variant: 'secondary' as const, icon: Clock, label: 'Pending' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.OPEN;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getComplaintTypeBadge = (type: string) => {
    const typeConfig = {
      REPAIR: 'bg-red-100 text-red-800',
      MAINTENANCE: 'bg-blue-100 text-blue-800',
      INSTALLATION: 'bg-green-100 text-green-800',
      INSPECTION: 'bg-yellow-100 text-yellow-800',
      WARRANTY: 'bg-purple-100 text-purple-800',
      OTHER: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeConfig[type as keyof typeof typeConfig] || typeConfig.OTHER}`}>
        {type}
      </span>
    );
  };

  const columns = [
    {
      header: 'Report Date',
      accessorKey: 'reportDate',
      cell: ({ row }: any) => format(new Date(row.original.reportDate), 'MMM dd, yyyy'),
    },
    {
      header: 'Customer',
      accessorKey: 'customer.name',
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.customer.name}</div>
          <div className="text-sm text-muted-foreground">{row.original.customer.city}</div>
        </div>
      ),
    },
    {
      header: 'Nature of Service',
      accessorKey: 'natureOfService',
      cell: ({ row }: any) => (
        <div className="max-w-[200px] truncate" title={row.original.natureOfService}>
          {row.original.natureOfService}
        </div>
      ),
    },
    {
      header: 'Type',
      accessorKey: 'complaintType',
      cell: ({ row }: any) => getComplaintTypeBadge(row.original.complaintType),
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }: any) => getStatusBadge(row.original.status),
    },
    {
      header: 'Executive',
      accessorKey: 'executive.name',
    },
    {
      header: 'Visit Date',
      accessorKey: 'visitDate',
      cell: ({ row }: any) => 
        row.original.visitDate ? format(new Date(row.original.visitDate), 'MMM dd, yyyy') : '-',
    },
    {
      header: 'Completion Date',
      accessorKey: 'completionDate',
      cell: ({ row }: any) => 
        row.original.completionDate ? format(new Date(row.original.completionDate), 'MMM dd, yyyy') : '-',
    },
    {
      header: 'Details',
      accessorKey: 'details',
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">
          {row.original.details.length} item{row.original.details.length !== 1 ? 's' : ''}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Service History
          </CardTitle>
          <CardDescription className="text-gray-100">
            Complete history of all service reports and activities.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Service History Table */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Service History
            </span>
            <Button
              onClick={handleExport}
              variant="secondary"
              size="sm"
              className="bg-white text-primary hover:bg-gray-100"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search service history..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={complaintTypeFilter} onValueChange={setComplaintTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="REPAIR">Repair</SelectItem>
                <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
                <SelectItem value="INSTALLATION">Installation</SelectItem>
                <SelectItem value="INSPECTION">Inspection</SelectItem>
                <SelectItem value="WARRANTY">Warranty</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <DataTable
            columns={columns}
            data={serviceReports}
            loading={loading}
            pagination={{
              pageIndex: currentPage - 1,
              pageSize: 20,
              pageCount: totalPages,
              onPageChange: (page) => setCurrentPage(page + 1),
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
