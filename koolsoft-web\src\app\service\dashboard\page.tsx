'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  Bar<PERSON>hart3,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  FileText,
  Users,
  Wrench,
  Calendar,
  Target,
  Activity
} from 'lucide-react';

interface ServiceStatistics {
  overview: {
    totalReports: number;
    openReports: number;
    completedReports: number;
    pendingReports: number;
    recentReports: number;
    completionRate: number;
    averageResolutionTime: number;
    totalDetails: number;
    detailsWithParts: number;
    uniqueMachineTypes: number;
    uniqueSerialNumbers: number;
    partReplacementRate: number;
  };
  period: {
    name: string;
    startDate: string;
    endDate: string;
    totalReports: number;
    completedReports: number;
    completionRate: number;
  };
  breakdowns: {
    status: Array<{ status: string; count: number }>;
    complaintType: Array<{ complaintType: string; count: number }>;
  };
  insights: {
    mostCommonProblems: Array<{ problem: string; count: number }>;
    mostReplacedParts: Array<{ part: string; count: number }>;
  };
}

export default function ServiceDashboardPage() {
  const [statistics, setStatistics] = useState<ServiceStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('MONTH');

  useEffect(() => {
    loadStatistics();
  }, [period]);

  const loadStatistics = async () => {
    try {
      const response = await fetch(`/api/service/statistics?period=${period}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setStatistics(data.statistics);
      } else {
        toast.error('Failed to load service statistics');
      }
    } catch (error) {
      console.error('Error loading service statistics:', error);
      toast.error('Failed to load service statistics');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      OPEN: 'text-orange-600',
      IN_PROGRESS: 'text-blue-600',
      COMPLETED: 'text-green-600',
      CANCELLED: 'text-red-600',
      PENDING: 'text-yellow-600',
    };
    return colors[status as keyof typeof colors] || 'text-gray-600';
  };

  const getComplaintTypeColor = (type: string) => {
    const colors = {
      REPAIR: 'text-red-600',
      MAINTENANCE: 'text-blue-600',
      INSTALLATION: 'text-green-600',
      INSPECTION: 'text-yellow-600',
      WARRANTY: 'text-purple-600',
      OTHER: 'text-gray-600',
    };
    return colors[type as keyof typeof colors] || 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Service Dashboard</h1>
          <p className="text-muted-foreground">Loading service metrics and analytics...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Service Dashboard</h1>
          <p className="text-muted-foreground">Failed to load service statistics.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Service Dashboard</h1>
          <p className="text-muted-foreground">
            Service metrics and analytics for {statistics.period.name.toLowerCase()} period.
          </p>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="WEEK">This Week</SelectItem>
            <SelectItem value="MONTH">This Month</SelectItem>
            <SelectItem value="QUARTER">This Quarter</SelectItem>
            <SelectItem value="YEAR">This Year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                <p className="text-2xl font-bold">{statistics.overview.totalReports}</p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Open Reports</p>
                <p className="text-2xl font-bold">{statistics.overview.openReports}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{statistics.overview.completedReports}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{statistics.overview.completionRate.toFixed(1)}%</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Period Statistics */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {statistics.period.name} Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Reports This {statistics.period.name}</p>
              <p className="text-3xl font-bold">{statistics.period.totalReports}</p>
            </div>
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Completed This {statistics.period.name}</p>
              <p className="text-3xl font-bold">{statistics.period.completedReports}</p>
            </div>
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">{statistics.period.name} Completion Rate</p>
              <p className="text-3xl font-bold">{statistics.period.completionRate.toFixed(1)}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Resolution Time</p>
                <p className="text-2xl font-bold">{statistics.overview.averageResolutionTime.toFixed(1)} days</p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Service Details</p>
                <p className="text-2xl font-bold">{statistics.overview.totalDetails}</p>
              </div>
              <Wrench className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Part Replacement Rate</p>
                <p className="text-2xl font-bold">{statistics.overview.partReplacementRate.toFixed(1)}%</p>
              </div>
              <Activity className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unique Machines</p>
                <p className="text-2xl font-bold">{statistics.overview.uniqueSerialNumbers}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Breakdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Breakdown */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Status Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {statistics.breakdowns.status.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <span className={`font-medium ${getStatusColor(item.status)}`}>
                    {item.status.replace('_', ' ')}
                  </span>
                  <span className="font-bold">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Complaint Type Breakdown */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Complaint Type Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {statistics.breakdowns.complaintType.map((item) => (
                <div key={item.complaintType} className="flex items-center justify-between">
                  <span className={`font-medium ${getComplaintTypeColor(item.complaintType)}`}>
                    {item.complaintType}
                  </span>
                  <span className="font-bold">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Most Common Problems */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Most Common Problems
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {statistics.insights.mostCommonProblems.slice(0, 5).map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium truncate flex-1 mr-2" title={item.problem}>
                    {item.problem}
                  </span>
                  <span className="font-bold text-sm">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Most Replaced Parts */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Most Replaced Parts
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {statistics.insights.mostReplacedParts.slice(0, 5).map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium truncate flex-1 mr-2" title={item.part}>
                    {item.part}
                  </span>
                  <span className="font-bold text-sm">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
